{"name": "My workflow 2", "nodes": [{"parameters": {}, "type": "n8n-nodes-base.manualTrigger", "typeVersion": 1, "position": [-4288, -1008], "id": "1d0e38c9-f314-4d92-933e-7e90212b465c", "name": "When clicking ‘Execute workflow’"}, {"parameters": {"jsCode": "// PREPARE LEADS NODE - Clean business name and phone\nconst items = $input.all();\n\nconst preparedLeads = items.map((item, index) => {\n  const data = item.json;\n  \n  // Clean business name - remove Lithuanian characters and special chars\n  let cleanBusinessName = data.business_name || data.title || data.name || '';\n  cleanBusinessName = cleanBusinessName\n    .replace(/[ąčęėįšųūž]/gi, match => {\n      const replacements = {\n        'ą': 'a', 'Ą': 'A', 'č': 'c', 'Č': 'C', 'ę': 'e', 'Ę': 'E',\n        'ė': 'e', 'Ė': 'E', 'į': 'i', 'Į': 'I', 'š': 's', 'Š': 'S',\n        'ų': 'u', 'Ų': 'U', 'ū': 'u', 'Ū': 'U', 'ž': 'z', 'Ž': 'Z'\n      };\n      return replacements[match] || match;\n    })\n    .replace(/[,|]/g, '') // Remove commas and pipes\n    .trim();\n\n  // Clean phone number - remove all non-digits and add +370 if needed\n  let cleanPhone = '';\n  const phoneFields = [data.google_phone, data.phone, data.phoneNumber, data.owner_phone];\n  \n  for (const phoneField of phoneFields) {\n    if (phoneField && phoneField.trim()) {\n      cleanPhone = phoneField.replace(/\\D/g, '');\n      if (cleanPhone.startsWith('370')) {\n        cleanPhone = '+' + cleanPhone;\n      } else if (cleanPhone.startsWith('8') && cleanPhone.length === 9) {\n        cleanPhone = '+370' + cleanPhone.substring(1);\n      } else if (!cleanPhone.startsWith('+370') && cleanPhone.length === 8) {\n        cleanPhone = '+370' + cleanPhone;\n      }\n      if (cleanPhone.length >= 12) break; // Valid phone found\n    }\n  }\n\n  return {\n    ...data,\n    clean_business_name: cleanBusinessName,\n    clean_phone: cleanPhone,\n    row_index: index + 2 // Google Sheets is 1-indexed, +1 for header\n  };\n});\n\n// Filter out items without valid phone numbers\nconst validLeads = preparedLeads.filter(lead => lead.clean_phone && lead.clean_phone.length >= 12);\n\nreturn validLeads.map(lead => ({ json: lead }));"}, "id": "f0ec267d-3b46-41c1-ae1e-8a49d455aad9", "name": "Prepare Leads3", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [-2768, -928]}, {"parameters": {"operation": "getAll", "tableId": "sms_tracking", "limit": 1000}, "id": "509d16fb-1283-4de0-acfc-20400881e64b", "name": "Check Database for Existing Phones3", "type": "n8n-nodes-base.supabase", "typeVersion": 1, "position": [-2592, -928], "credentials": {"supabaseApi": {"id": "UreeSglD1aITdAps", "name": "Supabase account"}}}, {"parameters": {"jsCode": "// DATABASE CHECK AND SHEET MARKING\nconst leads = $('Prepare Leads3').all();\nconst existingPhones = $('Check Database for Existing Phones3').all();\n\n// Create set of existing phone numbers for fast lookup\nconst phoneSet = new Set(existingPhones.map(item => item.json.phone_number));\n\nconst processedLeads = leads.map(lead => {\n  const data = lead.json;\n  const phoneExists = phoneSet.has(data.clean_phone);\n  \n  return {\n    ...data,\n    sms_sent: phoneExists ? 'yes' : 'no',\n    sms_status: phoneExists ? 'already_sent' : 'unsent',\n    phone_exists_in_db: phoneExists\n  };\n});\n\nreturn processedLeads.map(lead => ({ json: lead }));"}, "id": "13e82342-a462-47ac-af6e-6cd68d002ba7", "name": "Mark SMS Status in Data3", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [-2384, -928]}, {"parameters": {"operation": "update", "documentId": {"__rl": true, "value": "1nE2TryBqJyb5j9euNlSKgCCnMmjW2IITo3PQgDFUEWw", "mode": "list", "cachedResultName": "Leads", "cachedResultUrl": "https://docs.google.com/spreadsheets/d/1nE2TryBqJyb5j9euNlSKgCCnMmjW2IITo3PQgDFUEWw/edit?usp=drivesdk"}, "sheetName": {"__rl": true, "value": "gid=0", "mode": "list", "cachedResultName": "Sheet1", "cachedResultUrl": "https://docs.google.com/spreadsheets/d/1nE2TryBqJyb5j9euNlSKgCCnMmjW2IITo3PQgDFUEWw/edit#gid=0"}, "columns": {"mappingMode": "defineBelow", "value": {"sms_sent": "={{ $json.sms_sent }}", "sms_status": "={{ $json.sms_status }}", "address": "={{ $json.address }}", "google_phone": "={{ $json.google_phone }}", "business_name": "={{ $json.business_name }}"}, "matchingColumns": ["address"], "schema": [{"id": "business_name", "displayName": "business_name", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true}, {"id": "address", "displayName": "address", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true, "removed": false}, {"id": "google_phone", "displayName": "google_phone", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true}, {"id": "website", "displayName": "website", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true}, {"id": "owner_phone", "displayName": "owner_phone", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true}, {"id": "phone_source", "displayName": "phone_source", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true}, {"id": "sms_sent", "displayName": "sms_sent", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true}, {"id": "sms_status", "displayName": "sms_status", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true}, {"id": "reply_received", "displayName": "reply_received", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true}, {"id": "notes", "displayName": "notes", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true}, {"id": "google_maps_url", "displayName": "google_maps_url", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true}, {"id": "category", "displayName": "category", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true}, {"id": "rating", "displayName": "rating", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true}, {"id": "reviews_count", "displayName": "reviews_count", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true}, {"id": "row_number", "displayName": "row_number", "required": false, "defaultMatch": false, "display": true, "type": "number", "canBeUsedToMatch": true, "readOnly": true, "removed": true}], "attemptToConvertTypes": false, "convertFieldsToString": false}, "options": {"cellFormat": "USER_ENTERED"}}, "id": "ea998bbc-5534-47b9-99b7-ed45fd43c581", "name": "Update Google Sheet Status3", "type": "n8n-nodes-base.googleSheets", "typeVersion": 4.6, "position": [-2176, -896], "credentials": {"googleSheetsOAuth2Api": {"id": "oKljuM10DCL4C6sO", "name": "Google Sheets account"}}}, {"parameters": {"jsCode": "// FILTER FOR SMS - Only leads where sms_sent is 'no'\nconst leads = $input.all();\n\nconst unsentLeads = leads.filter(lead => {\n  return lead.json.sms_sent === 'no';\n});\n\nconsole.log(`Filtered ${unsentLeads.length} leads for SMS sending (out of ${leads.length} total)`);\nconsole.log('Leads with sms_sent=\"no\":', unsentLeads.map(lead => ({\n  business_name: lead.json.business_name,\n  sms_sent: lead.json.sms_sent,\n  phone: lead.json.clean_phone\n})));\n\nreturn unsentLeads;"}, "id": "24f35436-24e2-4758-9ed0-2477fc636224", "name": "Filter Leads for SMS3", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [-1984, -880]}, {"parameters": {"jsCode": "// PROCESS SMS RESPONSE - Update status based on SMS results\nconst smsResults = $input.all();\n\nconsole.log('=== PROCESSING SMS RESPONSES ===');\nconsole.log(`Total SMS responses: ${smsResults.length}`);\n\nconst processedResults = smsResults.map((result, index) => {\n  const originalData = result.json;\n  const smsResponse = originalData.sms_response || '';\n  \n  console.log(`SMS ${index + 1} Response:`, {\n    business_name: originalData.business_name,\n    phone: originalData.clean_phone,\n    raw_response: smsResponse\n  });\n  \n  // Check SMS8 response format - look for success indicators in text response\n  const smsSuccess = smsResponse.includes('success') || smsResponse.includes('sent') || smsResponse.includes('delivered') || smsResponse.includes('OK');\n  \n  console.log(`SMS ${index + 1} Success:`, smsSuccess);\n  \n  return {\n    // Keep ALL original data from Set node\n    ...originalData,\n    // Update SMS status based on response\n    sms_status: smsSuccess ? 'success' : 'failed',\n    sms_error: smsSuccess ? null : smsResponse,\n    // Ensure key fields are preserved\n    address: originalData.address,\n    business_name: originalData.business_name,\n    clean_phone: originalData.clean_phone,\n    sms_sent: smsSuccess ? 'yes' : 'no'\n  };\n});\n\nconsole.log('=== FINAL PROCESSING RESULTS ===');\nprocessedResults.forEach((result, index) => {\n  console.log(`Result ${index + 1}:`, {\n    business_name: result.business_name,\n    sms_status: result.sms_status,\n    sms_sent: result.sms_sent\n  });\n});\n\nreturn processedResults.map(result => ({ json: result }));"}, "id": "4a4203d8-a524-4621-955e-8cc4b72e95a8", "name": "Process SMS Response3", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [-1360, -912]}, {"parameters": {"operation": "update", "documentId": {"__rl": true, "value": "1nE2TryBqJyb5j9euNlSKgCCnMmjW2IITo3PQgDFUEWw", "mode": "list", "cachedResultName": "Leads", "cachedResultUrl": "https://docs.google.com/spreadsheets/d/1nE2TryBqJyb5j9euNlSKgCCnMmjW2IITo3PQgDFUEWw/edit?usp=drivesdk"}, "sheetName": {"__rl": true, "value": "gid=0", "mode": "list", "cachedResultName": "Sheet1", "cachedResultUrl": "https://docs.google.com/spreadsheets/d/1nE2TryBqJyb5j9euNlSKgCCnMmjW2IITo3PQgDFUEWw/edit#gid=0"}, "columns": {"mappingMode": "defineBelow", "value": {"sms_status": "={{ $json.sms_status }}", "address": "={{ $json.address }}", "business_name": "={{ $json.business_name }}"}, "matchingColumns": ["address"], "schema": [{"id": "business_name", "displayName": "business_name", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true}, {"id": "address", "displayName": "address", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true, "removed": false}, {"id": "google_phone", "displayName": "google_phone", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true}, {"id": "website", "displayName": "website", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true}, {"id": "owner_phone", "displayName": "owner_phone", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true}, {"id": "phone_source", "displayName": "phone_source", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true}, {"id": "sms_sent", "displayName": "sms_sent", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true}, {"id": "sms_status", "displayName": "sms_status", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true}, {"id": "reply_received", "displayName": "reply_received", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true}, {"id": "notes", "displayName": "notes", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true}, {"id": "google_maps_url", "displayName": "google_maps_url", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true}, {"id": "category", "displayName": "category", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true}, {"id": "rating", "displayName": "rating", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true}, {"id": "reviews_count", "displayName": "reviews_count", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true}, {"id": "row_number", "displayName": "row_number", "required": false, "defaultMatch": false, "display": true, "type": "number", "canBeUsedToMatch": true, "readOnly": true, "removed": true}], "attemptToConvertTypes": false, "convertFieldsToString": false}, "options": {}}, "id": "350d4490-3744-42db-99e9-ff270553a39b", "name": "Update Final SMS Status3", "type": "n8n-nodes-base.googleSheets", "typeVersion": 4.6, "position": [-1104, -912], "credentials": {"googleSheetsOAuth2Api": {"id": "oKljuM10DCL4C6sO", "name": "Google Sheets account"}}}, {"parameters": {"tableId": "sms_tracking", "fieldsUi": {"fieldValues": [{"fieldId": "phone_number", "fieldValue": "={{ $json.clean_phone }}"}, {"fieldId": "business_name", "fieldValue": "={{ $json.business_name }}"}, {"fieldId": "sms_status", "fieldValue": "={{ $json.sms_status }}"}, {"fieldId": "address", "fieldValue": "={{ $json.address }}"}]}}, "id": "8280db08-9823-451f-9047-39c5a2d7d3ae", "name": "Record Successful SMS to Database3", "type": "n8n-nodes-base.supabase", "typeVersion": 1, "position": [-624, -944], "executeOnce": false, "credentials": {"supabaseApi": {"id": "UreeSglD1aITdAps", "name": "Supabase account"}}}, {"parameters": {"jsCode": "// Realistic dummy data for testing the workflow\n// This simulates the output from Apify Google Maps scraper\nconst mockBusinessData = [\n  {\n    \"title\": \"Gėlių Namai\",\n    \"address\": \"Gedimino pr. 15, Vilnius, Lithuania\",\n    \"phone\": \"+***********\",\n    \"phoneNumber\": \"+***********\",\n    \"website\": \"\", // No website - will be filtered\n    \"url\": \"https://maps.google.com/place/geliu-namai-vilnius\",\n    \"categoryName\": \"Gėlių parduotuvė\",\n    \"category\": \"Gėlių parduotuvė\",\n    \"totalScore\": 4.3,\n    \"rating\": 4.3,\n    \"reviewsCount\": 28,\n    \"location\": \"Gedimino pr. 15, Vilnius, Lithuania\",\n    \"name\": \"Gėlių Namai\"\n  },\n  {\n    \"title\": \"<PERSON><PERSON><PERSON>\",\n    \"address\": \"Savanorių pr. 23, Kaunas, Lithuania\",\n    \"phone\": \"+***********\",\n    \"phoneNumber\": \"+***********\",\n    \"website\": \"\", // No website - will be filtered\n    \"url\": \"https://maps.google.com/place/kavine-sauletekis-kaunas\",\n    \"categoryName\": \"Kavinė\",\n    \"category\": \"Kavin<PERSON>\",\n    \"totalScore\": 4.7,\n    \"rating\": 4.7,\n    \"reviewsCount\": 45,\n    \"location\": \"Savanorių pr. 23, Kaunas, Lithuania\",\n    \"name\": \"Kavinė Saulėtekis\"\n  },\n  {\n    \"title\": \"Grožio Salonas Žvaigždė\",\n    \"address\": \"Manto g. 8, Klaipėda, Lithuania\",\n    \"phone\": \"+37060333333\",\n    \"phoneNumber\": \"+37060333333\",\n    \"website\": \"\", // No website - will be filtered\n    \"url\": \"https://maps.google.com/place/grozio-salonas-zvaigzde\",\n    \"categoryName\": \"Grožio salonas\",\n    \"category\": \"Grožio salonas\",\n    \"totalScore\": 4.1,\n    \"rating\": 4.1,\n    \"reviewsCount\": 19,\n    \"location\": \"Manto g. 8, Klaipėda, Lithuania\",\n    \"name\": \"Grožio Salonas Žvaigždė\"\n  },\n  {\n    \"title\": \"Restoranas Šiaurės Žvaigždė\",\n    \"address\": \"Tilžės g. 109, Šiauliai, Lithuania\",\n    \"phone\": \"+37060444444\",\n    \"phoneNumber\": \"+37060444444\",\n    \"website\": \"\", // No website - will be filtered\n    \"url\": \"https://maps.google.com/place/restoranas-siaures-zvaigzde\",\n    \"categoryName\": \"Restoranas\",\n    \"category\": \"Restoranas\",\n    \"totalScore\": 4.5,\n    \"rating\": 4.5,\n    \"reviewsCount\": 67,\n    \"location\": \"Tilžės g. 109, Šiauliai, Lithuania\",\n    \"name\": \"Restoranas Šiaurės Žvaigždė\"\n  },\n  {\n    \"title\": \"Autoservisas Greitas Ratas\",\n    \"address\": \"Pramonės g. 45, Vilnius, Lithuania\",\n    \"phone\": \"+***********\",\n    \"phoneNumber\": \"+***********\",\n    \"website\": \"\", // No website - will be filtered\n    \"url\": \"https://maps.google.com/place/autoservisas-greitas-ratas\",\n    \"categoryName\": \"Autoservisas\",\n    \"category\": \"Autoservisas\",\n    \"totalScore\": 3.9,\n    \"rating\": 3.9,\n    \"reviewsCount\": 34,\n    \"location\": \"Pramonės g. 45, Vilnius, Lithuania\",\n    \"name\": \"Autoservisas Greitas Ratas\"\n  }\n];\n\nconsole.log('Mock Business Data - Generated test data:', mockBusinessData.length, 'businesses');\nreturn mockBusinessData.map(data => ({ json: data }));"}, "id": "19556abe-f378-4a37-ae76-122eac37afa9", "name": "Test Business Data3", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [-4064, -992]}, {"parameters": {"jsCode": "// Filter businesses without websites and format data\nconst businesses = $input.all();\nconst filteredLeads = [];\nconst seenBusinesses = new Set(); // Track duplicates within this batch\n\nfor (const item of businesses) {\n  const data = item.json;\n  \n  // Check if business has no website or website is empty/null\n  if (!data.website || data.website === '' || data.website === null) {\n    const businessName = data.title || data.name || 'Unknown';\n    const address = data.address || data.location || 'Unknown';\n    \n    // Create a unique key for deduplication (business name + address)\n    const uniqueKey = `${businessName.toLowerCase().trim()}_${address.toLowerCase().trim()}`;\n    \n    // Skip if we've already seen this business in this batch\n    if (seenBusinesses.has(uniqueKey)) {\n      console.log(`Skipping duplicate business: ${businessName}`);\n      continue;\n    }\n    \n    seenBusinesses.add(uniqueKey);\n    \n    const lead = {\n      business_name: businessName,\n      address: address,\n      google_phone: data.phone || data.phoneNumber || '',\n      website: '', // Empty since we're filtering for no website\n      owner_phone: '', // Will be filled later from rekvizitai\n      phone_source: 'pending', // pending/owner/google/missing\n      sms_sent: 'pending', // pending/success/failed\n      sms_status: '',\n      reply_received: 'no',\n      notes: 'Lead from Google Maps - no website found',\n      google_maps_url: data.url || '',\n      category: data.categoryName || data.category || '',\n      rating: data.totalScore || data.rating || '',\n      reviews_count: data.reviewsCount || '',\n      director_name: '', // Will be filled from rekvizitai\n      owner_email: '' // Will be filled from rekvizitai\n    };\n    \n    filteredLeads.push(lead);\n  }\n}\n\nconsole.log(`Found ${filteredLeads.length} unique businesses without websites (removed ${businesses.length - filteredLeads.length} duplicates)`);\n\nreturn filteredLeads.map(lead => ({ json: lead }));"}, "id": "3663c34f-2305-4e26-a9e2-af1688a9941f", "name": "Filter Businesses Without Website3", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [-3792, -1008]}, {"parameters": {"operation": "removeItemsSeenInPreviousExecutions", "dedupeValue": "={{ $json.address }}", "options": {}}, "type": "n8n-nodes-base.removeDuplicates", "typeVersion": 2, "position": [-3184, -944], "id": "7b77c5a3-f262-4713-8748-827de7807609", "name": "Remove Duplicates3"}, {"parameters": {"operation": "append", "documentId": {"__rl": true, "value": "1p2OxinsL6Kx7Tgd4NQtlFjCSZ4PV1p-Mjo3GoezwXbw", "mode": "list", "cachedResultName": "Leads_Unfiltered", "cachedResultUrl": "https://docs.google.com/spreadsheets/d/1p2OxinsL6Kx7Tgd4NQtlFjCSZ4PV1p-Mjo3GoezwXbw/edit?usp=drivesdk"}, "sheetName": {"__rl": true, "value": "gid=0", "mode": "list", "cachedResultName": "Sheet1", "cachedResultUrl": "https://docs.google.com/spreadsheets/d/1p2OxinsL6Kx7Tgd4NQtlFjCSZ4PV1p-Mjo3GoezwXbw/edit#gid=0"}, "columns": {"mappingMode": "defineBelow", "value": {"business_name": "={{ $json.business_name }}", "address": "={{ $json.address }}", "google_phone": "={{ $json.google_phone.replace('+', '') }}", "website": "={{ $json.website }}", "owner_phone": "={{ $json.owner_phone }}", "phone_source": "={{ $json.phone_source }}", "sms_sent": "={{ $json.sms_status }}", "sms_status": "={{ $json.sms_status }}", "reviews_count": "={{ $json.reviews_count }}", "rating": "={{ $json.rating }}", "category": "={{ $json.category }}", "google_maps_url": "={{ $json.google_maps_url }}", "notes": "={{ $json.notes }}", "reply_received": "={{ $json.reply_received }}"}, "matchingColumns": [], "schema": [{"id": "business_name", "displayName": "business_name", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true}, {"id": "address", "displayName": "address", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true}, {"id": "google_phone", "displayName": "google_phone", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true}, {"id": "website", "displayName": "website", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true}, {"id": "owner_phone", "displayName": "owner_phone", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true}, {"id": "phone_source", "displayName": "phone_source", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true}, {"id": "sms_sent", "displayName": "sms_sent", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true}, {"id": "sms_status", "displayName": "sms_status", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true}, {"id": "reply_received", "displayName": "reply_received", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true}, {"id": "notes", "displayName": "notes", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true}, {"id": "google_maps_url", "displayName": "google_maps_url", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true}, {"id": "category", "displayName": "category", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true}, {"id": "rating", "displayName": "rating", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true}, {"id": "reviews_count", "displayName": "reviews_count", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true}], "attemptToConvertTypes": false, "convertFieldsToString": false}, "options": {}}, "id": "8382d061-0416-4b85-b24d-7a623ed288a2", "name": "Unfiltered_Leads3", "type": "n8n-nodes-base.googleSheets", "typeVersion": 4.6, "position": [-3360, -976], "credentials": {"googleSheetsOAuth2Api": {"id": "oKljuM10DCL4C6sO", "name": "Google Sheets account"}}}, {"parameters": {"operation": "append", "documentId": {"__rl": true, "value": "1nE2TryBqJyb5j9euNlSKgCCnMmjW2IITo3PQgDFUEWw", "mode": "list", "cachedResultName": "Leads", "cachedResultUrl": "https://docs.google.com/spreadsheets/d/1nE2TryBqJyb5j9euNlSKgCCnMmjW2IITo3PQgDFUEWw/edit?usp=drivesdk"}, "sheetName": {"__rl": true, "value": "gid=0", "mode": "list", "cachedResultName": "Sheet1", "cachedResultUrl": "https://docs.google.com/spreadsheets/d/1nE2TryBqJyb5j9euNlSKgCCnMmjW2IITo3PQgDFUEWw/edit#gid=0"}, "columns": {"mappingMode": "defineBelow", "value": {"business_name": "={{ $json.business_name }}", "address": "={{ $json.address }}", "reviews_count": "={{ $json.reviews_count }}", "rating": "={{ $json.rating }}", "category": "={{ $json.category }}", "google_maps_url": "={{ $json.google_maps_url }}", "notes": "={{ $json.notes }}", "reply_received": "={{ $json.reply_received }}", "sms_status": "={{ $json.sms_status }}", "sms_sent": "={{ $json.sms_sent }}", "phone_source": "={{ $json.phone_source }}", "owner_phone": "={{ $json.owner_phone }}", "website": "={{ $json.website }}", "google_phone": "={{ $json.google_phone}}"}, "matchingColumns": [], "schema": [{"id": "business_name", "displayName": "business_name", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true}, {"id": "address", "displayName": "address", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true}, {"id": "google_phone", "displayName": "google_phone", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true}, {"id": "website", "displayName": "website", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true}, {"id": "owner_phone", "displayName": "owner_phone", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true}, {"id": "phone_source", "displayName": "phone_source", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true}, {"id": "sms_sent", "displayName": "sms_sent", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true}, {"id": "sms_status", "displayName": "sms_status", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true}, {"id": "reply_received", "displayName": "reply_received", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true}, {"id": "notes", "displayName": "notes", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true}, {"id": "google_maps_url", "displayName": "google_maps_url", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true}, {"id": "category", "displayName": "category", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true}, {"id": "rating", "displayName": "rating", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true}, {"id": "reviews_count", "displayName": "reviews_count", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true}], "attemptToConvertTypes": false, "convertFieldsToString": false}, "options": {}}, "id": "017b01b8-9b35-49ec-80d5-ef9c88efdedf", "name": "Filtered_Leads3", "type": "n8n-nodes-base.googleSheets", "typeVersion": 4.6, "position": [-2976, -928], "credentials": {"googleSheetsOAuth2Api": {"id": "oKljuM10DCL4C6sO", "name": "Google Sheets account"}}}, {"parameters": {"jsCode": "for (const item of $input.all()) {\n  // Normalize reviews_count\n  let count = item.json.reviews_count;\n  if (count === undefined || count === null || count === '') {\n    item.json.reviews_count = 0;\n  } else {\n    item.json.reviews_count = Number(count);\n  }\n\n  // Normalize rating\n  let rating = item.json.rating;\n  if (rating === undefined || rating === null || rating === '') {\n    item.json.rating = 0;\n  } else {\n    item.json.rating = Number(rating);\n  }\n}\n\nreturn $input.all();"}, "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [-3552, -976], "id": "cab2742d-3ffb-454f-9a06-8e99bddbfb9e", "name": "Normalise reviews, and3"}, {"parameters": {"url": "https://app.sms8.io/services/send.php", "sendQuery": true, "queryParameters": {"parameters": [{"name": "key", "value": "5b03d8491c90c112c20b5d0c3456a2ae93a16881"}, {"name": "number", "value": "={{ ($json.google_phone || $json.owner_phone).replace(/\\s+/g, '').replace(/[^0-9+]/g, '') }}"}, {"name": "message", "value": "=<PERSON><PERSON><PERSON>, <PERSON><PERSON> iš Upzera👋!\n\n<PERSON><PERSON> jauna komanda, pad<PERSON><PERSON> verslams sukurti svetaines ir pritraukti daugiau klientų pagerinant matomumą internetinėje erdvėje.\n\nNorėčiau trumpai pasidalinti idėja, kaip ne<PERSON>ti potencialių klientų – ar domintų sužinoti daugiau?\n\nupzera.com/lt"}, {"name": "device", "value": "[\"3771 | 0\"]"}, {"name": "type", "value": "sms"}, {"name": "prioritize", "value": "0"}]}, "sendBody": true, "bodyParameters": {"parameters": [{}]}, "options": {"response": {"response": {"responseFormat": "text", "outputPropertyName": "sms_response"}}}}, "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [-1776, -800], "id": "431b598e-310f-402c-a585-7b2c7195055a", "name": "SMS3"}, {"parameters": {}, "type": "n8n-nodes-base.merge", "typeVersion": 3.2, "position": [-1600, -944], "id": "7ace7226-6f88-48fe-aa2d-1b941cd45686", "name": "Merge3"}, {"parameters": {"content": "Problem here"}, "type": "n8n-nodes-base.stickyNote", "position": [1584, -1312], "typeVersion": 1, "id": "b9710cb1-967b-4348-88f7-5f990acfa167", "name": "Sticky Note1"}, {"parameters": {"content": "Problem here"}, "type": "n8n-nodes-base.stickyNote", "position": [736, -1904], "typeVersion": 1, "id": "18566d09-df84-4be2-a0d2-98d192845445", "name": "Sticky Note2"}, {"parameters": {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict", "version": 2}, "conditions": [{"id": "6723066e-3664-4dfb-942e-fafcdc467fc7", "leftValue": "={{ $json.sms_status }}", "rightValue": "success", "operator": {"type": "string", "operation": "equals", "name": "filter.operator.equals"}}], "combinator": "and"}, "options": {}}, "type": "n8n-nodes-base.if", "typeVersion": 2.2, "position": [-912, -880], "id": "d2d665f2-2584-4ad0-a9c5-690f612ff567", "name": "If"}], "pinData": {"When clicking ‘Execute workflow’": [{"json": {"query": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "location": "Lietuva"}}]}, "connections": {"When clicking ‘Execute workflow’": {"main": [[{"node": "Test Business Data3", "type": "main", "index": 0}]]}, "Prepare Leads3": {"main": [[{"node": "Check Database for Existing Phones3", "type": "main", "index": 0}]]}, "Check Database for Existing Phones3": {"main": [[{"node": "Mark SMS Status in Data3", "type": "main", "index": 0}]]}, "Mark SMS Status in Data3": {"main": [[{"node": "Update Google Sheet Status3", "type": "main", "index": 0}]]}, "Update Google Sheet Status3": {"main": [[{"node": "Filter Leads for SMS3", "type": "main", "index": 0}]]}, "Filter Leads for SMS3": {"main": [[{"node": "Merge3", "type": "main", "index": 0}, {"node": "SMS3", "type": "main", "index": 0}]]}, "Process SMS Response3": {"main": [[{"node": "Update Final SMS Status3", "type": "main", "index": 0}]]}, "Test Business Data3": {"main": [[{"node": "Filter Businesses Without Website3", "type": "main", "index": 0}]]}, "Filter Businesses Without Website3": {"main": [[{"node": "Normalise reviews, and3", "type": "main", "index": 0}]]}, "Remove Duplicates3": {"main": [[{"node": "Filtered_Leads3", "type": "main", "index": 0}]]}, "Unfiltered_Leads3": {"main": [[{"node": "Remove Duplicates3", "type": "main", "index": 0}]]}, "Filtered_Leads3": {"main": [[{"node": "Prepare Leads3", "type": "main", "index": 0}]]}, "Normalise reviews, and3": {"main": [[{"node": "Unfiltered_Leads3", "type": "main", "index": 0}]]}, "SMS3": {"main": [[{"node": "Merge3", "type": "main", "index": 1}]]}, "Merge3": {"main": [[{"node": "Process SMS Response3", "type": "main", "index": 0}]]}, "Update Final SMS Status3": {"main": [[{"node": "If", "type": "main", "index": 0}]]}, "If": {"main": [[{"node": "Record Successful SMS to Database3", "type": "main", "index": 0}]]}}, "active": false, "settings": {"executionOrder": "v1"}, "versionId": "6005fddd-996e-4e83-81aa-18ffbb670996", "meta": {"templateCredsSetupCompleted": true, "instanceId": "e792ababaacd3609090c67925029f9ce56f8b2294355abafc6b7c9d2271c36bb"}, "id": "5JmQD48moWbfAuMi", "tags": []}